name: YX IPs and DNS

on:
  workflow_dispatch:
  schedule:
    - cron: '0 8,20 * * *' # 每12小时运行一次
  #workflow_dispatch: # 手动触发

jobs:
  run-and-commit:
    runs-on: ubuntu-latest
    environment: env  # 引用名为 'env' 的环境

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Run the yx_ips script
        env:
          CF_API_KEY: ${{ secrets.CF_API_KEY }}
          CF_ZONE_ID: ${{ secrets.CF_ZONE_ID }}
          CF_DOMAIN_NAME: ${{ secrets.CF_DOMAIN_NAME }}
        run: python yx_ips.py

      - name: Configure Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"

      - name: Pull latest changes
        run: |
          git pull origin main

      - name: Commit and push changes to repository
        run: |
          git add yx_ips.txt
          git commit -m "Update yx_ips.txt with new IPs" || echo "No changes to commit"
          git pull --rebase origin main || echo "No rebase necessary"
          git push origin main || echo "Nothing to push"
