# 管理员系统配置指南

## 概述

本系统已成功集成管理员功能，包括用户管理、备份管理等功能。所有管理员权限都在后端进行严格验证，确保安全性。

## 新增环境变量

### 必需的管理员配置

```bash
# 管理员邮箱（必需）
ADMIN_EMAIL=<EMAIL>

# 管理员API速率限制（可选，默认值如下）
RATE_LIMIT_ADMIN_COUNT=50
RATE_LIMIT_ADMIN_WINDOW=60000
```

### 现有环境变量（确保已配置）

```bash
# JWT密钥
JWT_SECRET=your-jwt-secret-key

# 数据库配置
# （Cloudflare D1数据库会自动配置）

# Google Drive备份配置（如需备份功能）
GOOGLE_DRIVE_ENABLED=true
GOOGLE_DRIVE_CREDENTIALS={"type":"service_account",...}
GOOGLE_DRIVE_FOLDER_ID=your-folder-id
BACKUP_RETENTION_COUNT=30

# 前端域名配置
FRONTEND_DOMAIN=https://your-domain.com

# 其他现有配置...
```

## 数据库更新

系统会自动添加 `is_disabled` 字段到 `users` 表。如果需要手动添加：

```sql
ALTER TABLE users ADD COLUMN is_disabled INTEGER DEFAULT 0;
```

## 管理员功能

### 1. 自动权限检测
- 用户登录后自动检查是否为管理员
- 管理员用户会看到"🔧 管理"按钮

### 2. 用户管理
- 查看所有用户列表
- 查看用户统计信息（总用户数、活跃用户、已禁用用户、总笔记数）
- 禁用/启用用户账号
- 防止管理员禁用自己的账号

### 3. 备份管理
- 查看备份状态和配置
- 手动触发备份
- 查看备份相关信息

## API端点

### 管理员专用端点（需要管理员权限）

```
GET  /api/admin/check           - 检查管理员权限
GET  /api/admin/users           - 获取用户列表
PUT  /api/admin/users/{id}/toggle - 禁用/启用用户
GET  /api/admin/backup/status   - 查看备份状态
POST /api/admin/backup          - 手动触发备份
```

## 安全特性

### 1. 后端权限验证
- 所有管理员API都进行严格的后端权限验证
- 验证JWT令牌有效性
- 检查用户是否被禁用
- 验证用户是否为管理员

### 2. 用户状态检查
- 所有需要认证的端点都会检查用户是否被禁用
- 被禁用的用户无法访问任何功能

### 3. 速率限制
- 管理员端点有专门的速率限制
- 默认：50次请求/分钟

## 使用说明

### 1. 设置管理员
1. 在环境变量中设置 `ADMIN_EMAIL`
2. 使用该邮箱注册/登录系统
3. 登录后会自动显示管理按钮

### 2. 管理用户
1. 点击"🔧 管理"按钮进入管理页面
2. 点击"刷新用户列表"查看所有用户
3. 使用"禁用"/"启用"按钮管理用户状态

### 3. 管理备份
1. 在管理页面点击"查看备份状态"
2. 点击"手动备份"触发备份操作

## 注意事项

1. **前端不可信任**：所有权限验证都在后端进行
2. **管理员保护**：管理员无法禁用自己的账号
3. **用户状态**：被禁用的用户无法登录或使用任何功能
4. **备份权限**：只有管理员可以查看备份状态和触发备份
5. **环境变量**：确保 `ADMIN_EMAIL` 环境变量正确配置

## 故障排除

### 管理按钮不显示
- 检查 `ADMIN_EMAIL` 环境变量是否正确设置
- 确认登录的邮箱与 `ADMIN_EMAIL` 完全匹配
- 检查浏览器控制台是否有错误信息

### 权限被拒绝
- 确认用户账号未被禁用
- 检查JWT令牌是否有效
- 验证 `ADMIN_EMAIL` 配置

### 用户列表加载失败
- 检查数据库连接
- 确认 `is_disabled` 字段已添加到数据库
- 查看后端错误日志

## 更新日志

- ✅ 添加管理员权限检查系统
- ✅ 实现用户禁用/启用功能
- ✅ 集成备份管理到管理员界面
- ✅ 添加用户状态检查到所有认证端点
- ✅ 实现前端管理界面
- ✅ 添加严格的后端权限验证
- ✅ 移除普通用户的备份访问权限
