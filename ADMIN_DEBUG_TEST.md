# Admin Login Debug Test Guide

## Issue Description
After implementing the admin management system, the admin button is not showing up for admin users after login.

## Debugging Steps

### 1. Check Environment Variables
Ensure the following environment variables are set correctly in your Cloudflare Workers:

```bash
ADMIN_EMAIL=<EMAIL>
JWT_SECRET=your-jwt-secret-key
```

### 2. Test Admin Check API Directly

Use the browser console or curl to test the admin check API:

```javascript
// In browser console after login
fetch('/api/admin/check', {
    method: 'GET',
    headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log('Admin check result:', data))
.catch(error => console.error('Admin check error:', error));
```

### 3. Check Browser Console Logs

After login, check the browser console for:
- "Checking admin status..."
- "Admin check result: ..."
- Any error messages

### 4. Verify Database User Email

Check if the user email in the database matches the ADMIN_EMAIL environment variable:

```sql
SELECT id, email FROM users WHERE email = '<EMAIL>';
```

### 5. Test Backend Admin Function

Add temporary logging to the `isAdminUser` function in `w.js`:

```javascript
async function isAdminUser(userId, env) {
  console.log('=== Admin Check Debug ===');
  console.log('User ID:', userId);
  console.log('Admin Email from env:', env.ADMIN_EMAIL);
  
  if (!env.ADMIN_EMAIL) {
    console.error('ADMIN_EMAIL environment variable is not set');
    return false;
  }

  try {
    const user = await env.DB.prepare('SELECT email FROM users WHERE id = ?').bind(userId).first();
    console.log('User email from DB:', user?.email);
    console.log('Emails match:', user && user.email === env.ADMIN_EMAIL);
    return user && user.email === env.ADMIN_EMAIL;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}
```

### 6. Expected Console Output

For a successful admin login, you should see:
```
Checking admin status...
Current JWT token exists: true
=== Admin Check Debug ===
User ID: 1
Admin Email from env: <EMAIL>
User email from DB: <EMAIL>
Emails match: true
Admin check result: {success: true, isAdmin: true}
Admin privileges confirmed
```

### 7. Common Issues and Solutions

#### Issue: Admin check returns `{success: false, error: "..."}`
- Check if JWT token is valid
- Verify user is not disabled in database
- Check if `checkUserStatus` function is working correctly

#### Issue: Admin check returns `{success: true, isAdmin: false}`
- Verify ADMIN_EMAIL environment variable is set correctly
- Check if user email in database matches ADMIN_EMAIL exactly (case-sensitive)
- Ensure user exists in database

#### Issue: Admin check throws network error
- Check CORS configuration
- Verify API endpoint is accessible
- Check if user is authenticated

### 8. Manual Admin Button Test

To temporarily force show the admin button for testing:

```javascript
// In browser console
document.getElementById('admin-btn').style.display = 'inline-block';
```

### 9. Database Field Check

Ensure the `is_disabled` field exists in the users table:

```sql
PRAGMA table_info(users);
```

If missing, add it manually:

```sql
ALTER TABLE users ADD COLUMN is_disabled INTEGER DEFAULT 0;
```

## Next Steps

1. Follow the debugging steps above
2. Check console logs for specific error messages
3. Verify environment variables are set correctly
4. Test the admin check API directly
5. Report specific error messages for further assistance
