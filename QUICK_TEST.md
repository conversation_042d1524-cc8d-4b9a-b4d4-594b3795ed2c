# 快速测试步骤

## 测试前准备

### 1. 确保环境变量设置
在 Cloudflare Workers 环境变量中设置：
```
ADMIN_EMAIL=<EMAIL>
```
（使用您要测试的邮箱地址）

### 2. 部署更新的代码
确保 `w.js` 和 `index.html` 都已更新并部署。

## 测试步骤

### 步骤1：测试基本登录功能

1. 打开网站
2. 打开浏览器开发者工具（F12）
3. 尝试登录
4. 查看控制台输出，应该看到：
   - "Showing notes section..."
   - "About to load notes - currentUser: [email], encryptionKey: true"
   - "LoadNotes skipped..." 或正常的笔记加载信息

**如果看到错误：**
- 记录完整的错误信息
- 检查 Network 标签页中的失败请求

### 步骤2：测试管理员功能

1. 登录成功后，查看控制台输出：
   - "Checking admin status..."
   - "Admin check result: {success: true, isAdmin: true/false}"

2. 如果 `isAdmin: true`，应该看到"🔧 管理"按钮

**如果没有看到管理按钮：**
- 确认 `ADMIN_EMAIL` 与登录邮箱完全一致
- 查看控制台是否有错误信息

### 步骤3：手动测试管理员API

在浏览器控制台中运行：

```javascript
// 测试管理员检查
fetch('/api/admin/check', {
    method: 'GET',
    headers: {
        'Authorization': localStorage.getItem('authToken'),
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log('Manual admin check:', data))
.catch(error => console.error('Manual admin check error:', error));
```

### 步骤4：测试用户列表（如果是管理员）

```javascript
// 测试用户列表API
fetch('/api/admin/users', {
    method: 'GET',
    headers: {
        'Authorization': localStorage.getItem('authToken'),
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log('Users list:', data))
.catch(error => console.error('Users list error:', error));
```

## 预期结果

### 正常情况下应该看到：

1. **登录成功后：**
   ```
   Showing notes section...
   About to load notes - currentUser: <EMAIL>, encryptionKey: true
   Checking admin status...
   Admin check result: {success: true, isAdmin: true}
   Admin privileges confirmed
   ```

2. **管理员用户：**
   - 看到"🔧 管理"按钮
   - 点击后能进入管理页面
   - 能看到用户列表和备份状态

3. **普通用户：**
   - 不显示管理按钮
   - 正常使用笔记功能

## 故障排除

### 如果登录后显示 "Internal server error"：

1. 查看浏览器控制台的错误信息
2. 检查 Network 标签页中失败的请求
3. 可能的原因：
   - 数据库字段问题
   - JWT验证失败
   - 用户状态检查失败

### 如果没有显示管理按钮：

1. 确认 `ADMIN_EMAIL` 环境变量正确设置
2. 确认登录邮箱与 `ADMIN_EMAIL` 完全匹配
3. 查看控制台中的管理员检查结果

### 如果笔记显示为空：

1. 检查是否有现有笔记数据
2. 查看控制台中的 loadNotes 相关信息
3. 可能需要创建新笔记来测试

## 临时解决方案

如果问题持续存在，可以使用以下临时解决方案：

### 1. 强制显示管理按钮（仅用于测试）
在浏览器控制台中运行：
```javascript
document.getElementById('admin-btn').style.display = 'inline-block';
```

### 2. 手动设置管理员状态
在浏览器控制台中运行：
```javascript
isAdmin = true;
showAdminButton();
```

### 3. 跳过用户状态检查
如果登录失败，问题可能在后端的用户状态检查。这已经在代码中添加了容错处理。

## 需要报告的信息

如果问题仍然存在，请提供：

1. **浏览器控制台的完整输出**
2. **Network 标签页中失败请求的详细信息**
3. **您的环境变量设置**（不要包含敏感信息）
4. **具体的错误步骤**

这将帮助快速定位和解决问题。
