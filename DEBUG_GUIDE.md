# 调试指南

## 问题诊断

### 1. 登录后提示 Internal server error，笔记显示为空

**可能原因：**
1. 数据库字段 `is_disabled` 不存在
2. 用户状态检查函数出错
3. JWT验证问题

**调试步骤：**

#### 步骤1：检查浏览器控制台
打开浏览器开发者工具（F12），查看：
- Console 标签页中的错误信息
- Network 标签页中的API请求状态

#### 步骤2：检查环境变量
确保以下环境变量已正确设置：
```bash
ADMIN_EMAIL=<EMAIL>
JWT_SECRET=your-jwt-secret
```

#### 步骤3：检查数据库
如果可以访问数据库，运行以下SQL检查：
```sql
-- 检查用户表结构
PRAGMA table_info(users);

-- 检查是否有 is_disabled 字段
SELECT name FROM pragma_table_info('users') WHERE name='is_disabled';

-- 手动添加字段（如果不存在）
ALTER TABLE users ADD COLUMN is_disabled INTEGER DEFAULT 0;
```

#### 步骤4：临时禁用用户状态检查
如果问题持续，可以临时注释掉用户状态检查：

在 `w.js` 中找到这行：
```javascript
// 检查用户状态（是否被禁用）
await checkUserStatus(userId, env);
```

临时注释掉：
```javascript
// 检查用户状态（是否被禁用）
// await checkUserStatus(userId, env);
```

### 2. 没有显示管理按钮

**可能原因：**
1. `ADMIN_EMAIL` 环境变量未设置
2. 登录邮箱与 `ADMIN_EMAIL` 不匹配
3. 管理员检查API失败

**调试步骤：**

#### 步骤1：检查环境变量
确保 `ADMIN_EMAIL` 环境变量设置正确：
```bash
ADMIN_EMAIL=<EMAIL>
```

#### 步骤2：检查登录邮箱
确保您使用的登录邮箱与 `ADMIN_EMAIL` 完全一致（包括大小写）。

#### 步骤3：查看浏览器控制台
登录后查看控制台输出：
- 应该看到 "Checking admin status..."
- 应该看到 "Admin check result: ..." 

#### 步骤4：手动测试管理员API
登录后，在浏览器控制台运行：
```javascript
// 测试管理员检查API
fetch('/api/admin/check', {
    method: 'GET',
    headers: {
        'Authorization': localStorage.getItem('authToken'),
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log('Admin check result:', data))
.catch(error => console.error('Admin check error:', error));
```

## 快速修复方案

### 方案1：重置数据库字段
如果数据库字段有问题，可以手动重置：

```sql
-- 删除可能有问题的字段
ALTER TABLE users DROP COLUMN is_disabled;

-- 重新添加字段
ALTER TABLE users ADD COLUMN is_disabled INTEGER DEFAULT 0;
```

### 方案2：临时禁用用户状态检查
在 `w.js` 中临时注释掉所有 `checkUserStatus` 调用：

```javascript
// 在所有需要认证的端点中，找到这行并注释掉：
// await checkUserStatus(userId, env);
```

### 方案3：强制显示管理按钮（仅用于测试）
在 `index.html` 中临时修改：

```javascript
// 在登录成功后强制显示管理按钮
setTimeout(() => {
    showNotesSection();
    
    // 强制显示管理按钮（仅用于测试）
    document.getElementById('admin-btn').style.display = 'inline-block';
    
    // 检查管理员权限
    checkAdminStatus();
}, 1000);
```

## 常见错误信息

### "Account has been disabled by administrator"
- 用户账号被禁用
- 检查数据库中该用户的 `is_disabled` 字段

### "Administrator privileges required"
- 用户不是管理员
- 检查 `ADMIN_EMAIL` 环境变量设置

### "ADMIN_EMAIL environment variable is not set"
- 环境变量未配置
- 在 Cloudflare Workers 设置中添加 `ADMIN_EMAIL`

### "Failed to check admin status"
- 数据库查询失败
- 检查数据库连接和表结构

## 联系支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误信息
2. Network 标签页中失败请求的详细信息
3. 您的环境变量配置（不要包含敏感信息）
4. 数据库表结构信息

## 成功标志

当一切正常工作时，您应该看到：
1. 登录成功后能正常加载笔记
2. 管理员用户能看到"🔧 管理"按钮
3. 点击管理按钮能进入管理页面
4. 管理页面能正常显示用户列表和备份状态
