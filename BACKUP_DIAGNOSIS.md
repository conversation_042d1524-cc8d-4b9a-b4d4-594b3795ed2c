# 备份功能诊断指南

## 🚨 **当前错误分析**

**错误信息：** `Upload failed: Not Found`

这个错误通常表示以下问题之一：

### **1. Google Drive Folder ID 问题** 🔍
- **可能原因：** `GOOGLE_DRIVE_FOLDER_ID` 环境变量中的文件夹ID不存在或不正确
- **检查方法：**
  1. 登录 Google Drive
  2. 进入您想要用作备份的文件夹
  3. 从URL中复制文件夹ID（例如：`https://drive.google.com/drive/folders/1ABC123DEF456` 中的 `1ABC123DEF456`）
  4. 确认环境变量中的ID与此完全匹配

### **2. Service Account 权限问题** 🔐
- **可能原因：** Service Account 没有访问指定文件夹的权限
- **解决方法：**
  1. 在 Google Drive 中找到备份文件夹
  2. 右键点击文件夹 → "共享"
  3. 添加您的 Service Account 邮箱地址（从 `GOOGLE_DRIVE_CREDENTIALS` 中的 `client_email` 字段获取）
  4. 给予 "编辑者" 权限

### **3. Google Drive API 权限问题** 📋
- **可能原因：** Service Account 的 API 权限配置不正确
- **检查项目：**
  1. Google Cloud Console 中启用了 Google Drive API
  2. Service Account 有正确的权限范围

## 🔧 **已添加的诊断功能**

我已经在代码中添加了以下诊断功能：

### **1. 文件夹访问验证**
```javascript
async verifyFolderAccess(accessToken) {
  // 在上传前验证文件夹是否存在和可访问
  // 提供详细的错误信息
}
```

### **2. 详细错误日志**
```javascript
console.error('Google Drive upload error:', {
  status: response.status,
  statusText: response.statusText,
  error: errorText,
  folderId: this.folderId
});
```

### **3. 配置验证**
```javascript
// 验证 credentials 格式
// 记录配置状态（不包含敏感信息）
```

## 🧪 **诊断步骤**

### **步骤1：检查环境变量**
确认以下环境变量已正确设置：

```bash
GOOGLE_DRIVE_ENABLED=true
GOOGLE_DRIVE_CREDENTIALS={"type":"service_account",...}
GOOGLE_DRIVE_FOLDER_ID=1ABC123DEF456
BACKUP_RETENTION_COUNT=30
```

### **步骤2：验证 Service Account 邮箱**
1. 从 `GOOGLE_DRIVE_CREDENTIALS` 中找到 `client_email` 字段
2. 确认这个邮箱地址已被添加到 Google Drive 备份文件夹的共享列表中

### **步骤3：测试备份功能**
1. 在管理页面点击 "View Backup Status"
2. 检查配置是否正确显示
3. 点击 "Manual Backup" 并查看详细错误信息

### **步骤4：查看控制台日志**
现在备份过程会输出详细的日志信息：
- 配置验证结果
- 文件夹访问验证
- 具体的错误详情

## 🔍 **常见问题解决方案**

### **问题1：404 Not Found**
```
错误：Google Drive folder not found: [FOLDER_ID]
解决：检查 GOOGLE_DRIVE_FOLDER_ID 是否正确
```

### **问题2：403 Forbidden**
```
错误：Access denied to Google Drive folder: [FOLDER_ID]
解决：将 Service Account 邮箱添加到文件夹共享
```

### **问题3：401 Unauthorized**
```
错误：Failed to get access token
解决：检查 GOOGLE_DRIVE_CREDENTIALS 格式和内容
```

### **问题4：Invalid credentials format**
```
错误：Invalid Google Drive credentials format
解决：确认 GOOGLE_DRIVE_CREDENTIALS 是有效的 JSON 格式
```

## 📋 **检查清单**

在测试备份功能前，请确认：

- [ ] ✅ Google Cloud Project 已创建
- [ ] ✅ Google Drive API 已启用
- [ ] ✅ Service Account 已创建并下载了 JSON 密钥文件
- [ ] ✅ `GOOGLE_DRIVE_CREDENTIALS` 包含完整的 JSON 密钥内容
- [ ] ✅ Google Drive 中已创建备份文件夹
- [ ] ✅ `GOOGLE_DRIVE_FOLDER_ID` 是正确的文件夹ID
- [ ] ✅ Service Account 邮箱已添加到备份文件夹的共享列表
- [ ] ✅ Service Account 有 "编辑者" 权限
- [ ] ✅ `GOOGLE_DRIVE_ENABLED=true`

## 🚀 **下一步操作**

1. **立即测试：** 点击 "Manual Backup" 按钮
2. **查看日志：** 检查浏览器控制台的详细错误信息
3. **验证权限：** 确认 Service Account 有文件夹访问权限
4. **检查配置：** 使用 "View Backup Status" 验证配置

## 💡 **提示**

- 新的诊断功能会在备份失败时提供更具体的错误信息
- 所有敏感信息都不会在日志中显示
- 如果文件夹验证失败，会在上传前就报告错误
- 备份成功后会自动清理旧的备份文件
