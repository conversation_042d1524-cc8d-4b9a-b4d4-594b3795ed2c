# OneDrive 备份功能配置指南

## 🎯 功能概述

现在系统支持两种备份方案：
- **Google Drive 备份**（原有功能，保持不变）
- **OneDrive 备份**（新增功能）

通过环境变量 `BACKUP_PROVIDER` 可以选择使用哪种备份方案。

## 📋 环境变量配置

### 1. 备份方案选择

```bash
# 选择备份提供商（可选值：google_drive, onedrive）
BACKUP_PROVIDER=onedrive  # 使用 OneDrive 备份
# 或
BACKUP_PROVIDER=google_drive  # 使用 Google Drive 备份（默认）
```

### 2. OneDrive 备份配置

如果选择 OneDrive 备份，需要配置以下环境变量：

```bash
# 启用 OneDrive 备份
ONEDRIVE_ENABLED=true

# OneDrive 应用凭据（JSON 格式）
ONEDRIVE_CREDENTIALS={"client_id":"your-client-id","client_secret":"your-client-secret","tenant_id":"your-tenant-id"}

# OneDrive Drive ID
ONEDRIVE_DRIVE_ID=your-drive-id

# 备份文件保留数量（可选，默认30个）
BACKUP_RETENTION_COUNT=30
```

### 3. Google Drive 备份配置（保持不变）

如果选择 Google Drive 备份，原有配置保持不变：

```bash
# 启用 Google Drive 备份
GOOGLE_DRIVE_ENABLED=true

# Google Drive 服务账号凭据（JSON 格式）
GOOGLE_DRIVE_CREDENTIALS={"type":"service_account",...}

# Google Drive 文件夹 ID
GOOGLE_DRIVE_FOLDER_ID=your-folder-id

# 备份文件保留数量（可选，默认30个）
BACKUP_RETENTION_COUNT=30
```

## 🔧 OneDrive 应用设置

### 1. 创建 Azure 应用

1. 访问 [Azure Portal](https://portal.azure.com/)
2. 进入 "Azure Active Directory" > "应用注册"
3. 点击 "新注册"
4. 填写应用信息：
   - 名称：例如 "CFCDN-Auto Backup"
   - 支持的账户类型：选择适合的选项
   - 重定向 URI：可以留空

### 2. 配置应用权限

1. 在应用页面，进入 "API 权限"
2. 点击 "添加权限" > "Microsoft Graph" > "应用程序权限"
3. 添加以下权限：
   - `Files.ReadWrite.All`
   - `Sites.ReadWrite.All`
4. 点击 "授予管理员同意"

### 3. 创建客户端密钥

1. 进入 "证书和密钥"
2. 点击 "新客户端密钥"
3. 设置描述和过期时间
4. 复制生成的密钥值（这是 `client_secret`）

### 4. 获取必要信息

- `client_id`：应用的应用程序（客户端）ID
- `client_secret`：刚才创建的客户端密钥
- `tenant_id`：Azure AD 租户 ID
- `drive_id`：OneDrive 的 Drive ID

### 5. 获取 Drive ID

可以通过以下方式获取 Drive ID：

1. **个人 OneDrive**：
   ```
   https://graph.microsoft.com/v1.0/me/drive
   ```

2. **SharePoint 站点**：
   ```
   https://graph.microsoft.com/v1.0/sites/{site-id}/drive
   ```

3. **特定用户的 OneDrive**：
   ```
   https://graph.microsoft.com/v1.0/users/{user-id}/drive
   ```

## 🚀 使用方法

### 1. 自动备份

系统会根据 `BACKUP_PROVIDER` 环境变量自动选择备份方案：

- 如果设置为 `onedrive`，使用 OneDrive 备份
- 如果设置为 `google_drive` 或未设置，使用 Google Drive 备份

### 2. 手动备份

管理员可以通过管理界面手动触发备份：

1. 登录管理界面
2. 查看备份状态：`GET /api/admin/backup/status`
3. 手动备份：`POST /api/admin/backup`

### 3. 备份状态查询

备份状态 API 现在会显示：
- 当前使用的备份提供商
- 备份是否启用
- 提供商特定的配置状态
- 备份文件保留数量

## 📝 备份文件格式

无论使用哪种备份方案，备份文件格式保持一致：

```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0",
  "data": {
    "users": [...],
    "notes": [...]
  },
  "stats": {
    "userCount": 10,
    "noteCount": 100,
    "backupSize": 12345
  }
}
```

## 🔍 故障排除

### OneDrive 备份常见问题

1. **认证失败**
   - 检查 `client_id`、`client_secret`、`tenant_id` 是否正确
   - 确认应用权限已正确配置并获得管理员同意

2. **Drive ID 错误**
   - 确认 `ONEDRIVE_DRIVE_ID` 是否正确
   - 检查应用是否有访问该 Drive 的权限

3. **上传失败**
   - 检查文件大小是否超过限制（当前实现支持 < 4MB）
   - 确认 OneDrive 存储空间是否充足

### 日志查看

系统会输出详细的日志信息，包括：
- 备份提供商选择
- 认证过程
- 上传进度
- 错误详情

## 🔄 迁移指南

### 从 Google Drive 迁移到 OneDrive

1. 配置 OneDrive 环境变量
2. 设置 `BACKUP_PROVIDER=onedrive`
3. 测试备份功能
4. 可选：保留 Google Drive 配置作为备用

### 从 OneDrive 迁移到 Google Drive

1. 确保 Google Drive 环境变量已配置
2. 设置 `BACKUP_PROVIDER=google_drive`
3. 测试备份功能

## ⚠️ 注意事项

1. **向后兼容性**：如果不设置 `BACKUP_PROVIDER`，系统默认使用 Google Drive 备份
2. **安全性**：请妥善保管应用凭据，不要在代码中硬编码
3. **权限**：确保应用有足够的权限访问 OneDrive
4. **监控**：建议定期检查备份状态和日志
