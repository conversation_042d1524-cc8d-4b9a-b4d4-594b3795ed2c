# Backblaze B2 备份功能配置指南

## 🎯 功能概述

系统现在支持三种备份方案：
- **Backblaze B2 备份**（新增功能，默认选项）
- **Google Drive 备份**（原有功能）
- **OneDrive 备份**（已有功能）

通过环境变量 `BACKUP_PROVIDER` 可以选择使用哪种备份方案。

## 📋 环境变量配置

### 1. 备份方案选择

```bash
# 选择备份提供商（可选值：backblaze, google_drive, onedrive）
BACKUP_PROVIDER=backblaze  # 使用 Backblaze B2 备份（默认）
# 或
BACKUP_PROVIDER=google_drive  # 使用 Google Drive 备份
# 或
BACKUP_PROVIDER=onedrive  # 使用 OneDrive 备份
```

### 2. Backblaze B2 备份配置

如果选择 Backblaze B2 备份，需要配置以下环境变量：

```bash
# 启用 Backblaze B2 备份
BACKBLAZE_ENABLED=true

# Backblaze B2 应用密钥 ID
BACKBLAZE_APPLICATION_KEY_ID=your-application-key-id

# Backblaze B2 应用密钥
BACKBLAZE_APPLICATION_KEY=your-application-key

# Backblaze B2 存储桶 ID
BACKBLAZE_BUCKET_ID=your-bucket-id

# 备份文件保留数量（可选，默认30个）
BACKUP_RETENTION_COUNT=30
```

## 🔧 Backblaze B2 设置步骤

### 1. 创建 Backblaze B2 账户

1. 访问 [Backblaze B2](https://www.backblaze.com/b2/cloud-storage.html)
2. 注册账户并完成验证
3. 登录到 Backblaze B2 控制台

### 2. 创建存储桶

1. 在 B2 控制台中，点击 "Buckets"
2. 点击 "Create a Bucket"
3. 输入存储桶名称（例如：`my-app-database-backups`）
4. 选择 "Private" 作为文件访问权限
5. 记录生成的 **Bucket ID**

### 3. 创建应用密钥

1. 在 B2 控制台中，点击 "App Keys"
2. 点击 "Add a New Application Key"
3. 配置应用密钥：
   - **Key Name**: 输入描述性名称（例如：`database-backup-key`）
   - **Allow access to Bucket(s)**: 选择刚创建的存储桶
   - **Type of Access**: 选择 "Read and Write"
   - **Allow List All Bucket Names**: 可选择 "Allow"
4. 点击 "Create New Key"
5. 记录生成的：
   - **keyID**（这是 `BACKBLAZE_APPLICATION_KEY_ID`）
   - **applicationKey**（这是 `BACKBLAZE_APPLICATION_KEY`）

⚠️ **重要**: 应用密钥只会显示一次，请立即保存！

### 4. 配置环境变量

在 Cloudflare Workers 环境中设置以下变量：

```bash
BACKUP_PROVIDER=backblaze
BACKBLAZE_ENABLED=true
BACKBLAZE_APPLICATION_KEY_ID=your-key-id-from-step-3
BACKBLAZE_APPLICATION_KEY=your-application-key-from-step-3
BACKBLAZE_BUCKET_ID=your-bucket-id-from-step-2
BACKUP_RETENTION_COUNT=30
```

## 🚀 使用方法

### 1. 自动备份

系统会根据 `BACKUP_PROVIDER` 环境变量自动选择备份方案：

- 如果设置为 `backblaze`，使用 Backblaze B2 备份
- 如果设置为 `google_drive`，使用 Google Drive 备份
- 如果设置为 `onedrive`，使用 OneDrive 备份
- 如果未设置，默认使用 Backblaze B2 备份

### 2. 手动备份

管理员可以通过管理界面手动触发备份：

1. 登录管理界面
2. 查看备份状态：`GET /api/admin/backup/status`
3. 手动备份：`POST /api/admin/backup`

### 3. 备份状态查询

备份状态 API 现在会显示：
- 当前使用的备份提供商
- 备份是否启用
- Backblaze B2 特定的配置状态
- 备份文件保留数量

## 📝 备份文件格式

备份文件格式与其他备份方案保持一致：

```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0",
  "data": {
    "users": [...],
    "notes": [...]
  },
  "stats": {
    "userCount": 10,
    "noteCount": 100,
    "backupSize": 12345
  }
}
```

文件命名格式：`database-backup-YYYY-MM-DDTHH-MM-SS-sssZ.json`

## 🔍 故障排除

### Backblaze B2 备份常见问题

1. **认证失败**
   - 检查 `BACKBLAZE_APPLICATION_KEY_ID` 和 `BACKBLAZE_APPLICATION_KEY` 是否正确
   - 确认应用密钥未过期且有效
   - 验证应用密钥权限是否包含目标存储桶

2. **存储桶访问错误**
   - 确认 `BACKBLAZE_BUCKET_ID` 是否正确
   - 检查应用密钥是否有访问该存储桶的权限
   - 验证存储桶是否存在且未被删除

3. **上传失败**
   - 检查文件大小是否在限制范围内
   - 确认 Backblaze B2 账户存储空间是否充足
   - 验证网络连接是否正常

4. **清理失败**
   - 检查应用密钥是否有删除文件的权限
   - 确认要删除的文件是否存在
   - 验证 `BACKUP_RETENTION_COUNT` 设置是否合理

### 日志查看

系统会输出详细的日志信息，包括：
- 备份提供商选择
- Backblaze B2 认证过程
- 文件上传进度
- 清理操作结果
- 错误详情

## 💰 成本考虑

Backblaze B2 的定价优势：
- 存储费用：$0.005/GB/月
- 下载费用：前 1GB 免费，之后 $0.01/GB
- API 调用：前 2,500 次免费，之后 $0.004/1,000 次
- 无最小存储时间要求

对于数据库备份场景，成本通常很低。

## 🔄 迁移指南

### 从其他备份方案迁移到 Backblaze B2

1. 配置 Backblaze B2 环境变量
2. 设置 `BACKUP_PROVIDER=backblaze`
3. 测试备份功能
4. 可选：保留原有配置作为备用

### 从 Backblaze B2 迁移到其他方案

1. 确保目标备份方案环境变量已配置
2. 设置相应的 `BACKUP_PROVIDER` 值
3. 测试备份功能

## ⚠️ 注意事项

1. **安全性**：请妥善保管应用密钥，不要在代码中硬编码
2. **权限**：确保应用密钥有足够的权限访问存储桶
3. **监控**：建议定期检查备份状态和日志
4. **测试**：在生产环境使用前，请先在测试环境验证配置
5. **备份验证**：定期验证备份文件的完整性和可恢复性

## 🔗 相关链接

- [Backblaze B2 官方文档](https://www.backblaze.com/b2/docs/)
- [Backblaze B2 API 参考](https://www.backblaze.com/b2/docs/b2_api.html)
- [Backblaze B2 定价](https://www.backblaze.com/b2/cloud-storage-pricing.html)
